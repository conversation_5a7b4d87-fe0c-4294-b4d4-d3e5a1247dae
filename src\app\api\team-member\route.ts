import mongoose from "mongoose";
import { NextResponse } from "next/server";

export async function GET(request: Request) {



    try {
           if (mongoose.connection.readyState !== 1) {
              await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => reject(new Error('Connection timeout')), 10000);
                mongoose.connection.once('connected', () => {
                  clearTimeout(timeout);
                  resolve(true);
                });
              });
            }

let allteamember=await 


    } catch (error: unknown) {
        // Safely extract message from the error
        let message = "An unknown error occurred";
        if (error instanceof Error) {
            message = error.message;
        }

        return NextResponse.json(
            {
                success: false,
                message,
            },
            { status: 500 }
        );
    }


}