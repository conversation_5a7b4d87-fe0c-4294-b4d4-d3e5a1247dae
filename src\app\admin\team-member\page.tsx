'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { PlusCircle, Loader2 } from 'lucide-react';
import { TeamMemberTable } from '@/components/admin/TeamMemberTable';
import { TeamMemberForm } from '@/components/admin/TeamMemberForm';
import type { TeamMember } from '@/lib/types';
import { useGetTeamMembersQuery } from '@/lib/redux/api/teamMemberApi';

export default function AdminTeamMemberPage() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingTeamMember, setEditingTeamMember] = useState<TeamMember | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Fetch team members using RTK Query
  const {
    data: teamMembers = [],
    isLoading,
    isError,
    error,
    refetch
  } = useGetTeamMembersQuery();

  const handleFormSubmit = () => {
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setEditingTeamMember(null);
    refetch(); // Refetch data after form submission
  };

  const handleEdit = (teamMember: TeamMember) => {
    setEditingTeamMember(teamMember);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setEditingTeamMember(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading team members...</span>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Failed to load team members. Please try again.</p>
            <Button onClick={() => refetch()} className="mt-4">
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Team Members</h1>
          <p className="text-muted-foreground">
            Manage your team members and their information.
          </p>
        </div>

        <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
          <DialogTrigger asChild>
            <Button>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Team Member
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add New Team Member</DialogTitle>
              <DialogDescription>
                Fill in the details to add a new team member.
              </DialogDescription>
            </DialogHeader>

            <TeamMemberForm onFormSubmit={handleFormSubmit} />
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Team Members ({teamMembers.length})</CardTitle>
          <CardDescription>
            A list of all team members in your organization.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <TeamMemberTable 
            teamMembers={teamMembers} 
            onEdit={handleEdit}
            onRefetch={refetch}
          />
        </CardContent>
      </Card>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={handleCloseEditModal}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Team Member</DialogTitle>
            <DialogDescription>
              Update the team member information.
            </DialogDescription>
          </DialogHeader>

          {editingTeamMember && (
            <TeamMemberForm 
              teamMember={editingTeamMember}
              onFormSubmit={handleFormSubmit} 
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
